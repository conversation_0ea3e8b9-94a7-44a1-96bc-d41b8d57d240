import { useState, useEffect } from "react";
import { web3Utils } from "../utils/web3.js";
import {
  CONTRACT_ADDRESSES,
  NFT_EXCHANGE_ABI,
  ERC721_COLLECTION_ABI,
  ERC1155_COLLECTION_ABI,
} from "../contracts/config.js";
import MarketplaceBrowser from "./MarketplaceBrowser.jsx";

const Marketplace = ({ account, collections }) => {
  const [activeTab, setActiveTab] = useState("list");
  const [selectedCollection, setSelectedCollection] = useState("");
  const [tokenIds, setTokenIds] = useState("");
  const [amounts, setAmounts] = useState("");
  const [prices, setPrices] = useState("");
  const [duration, setDuration] = useState("7"); // days
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [collectionType, setCollectionType] = useState("");

  useEffect(() => {
    if (selectedCollection) {
      const collection = collections.find(
        (c) => c.address === selectedCollection
      );
      setCollectionType(collection?.type || "");
    }
  }, [selectedCollection, collections]);

  const parseArrayInput = (input) => {
    return input
      .split(",")
      .map((item) => item.trim())
      .filter((item) => item);
  };

  const validateInputs = (isBatch = false) => {
    const errors = [];

    if (!selectedCollection) errors.push("Please select a collection");
    if (!tokenIds.trim()) errors.push("Please enter token ID(s)");
    if (!prices.trim()) errors.push("Please enter price(s)");
    if (parseInt(duration) <= 0) errors.push("Duration must be greater than 0");

    if (isBatch) {
      const tokenIdArray = parseArrayInput(tokenIds);
      const priceArray = parseArrayInput(prices);

      if (tokenIdArray.length !== priceArray.length) {
        errors.push("Number of token IDs must match number of prices");
      }

      if (collectionType === "ERC1155" && amounts.trim()) {
        const amountArray = parseArrayInput(amounts);
        if (amountArray.length !== tokenIdArray.length) {
          errors.push("Number of amounts must match number of token IDs");
        }
      }
    }

    return errors;
  };

  const approveNFT = async (contractAddress, tokenId = null) => {
    try {
      const abi =
        collectionType === "ERC721"
          ? ERC721_COLLECTION_ABI
          : ERC1155_COLLECTION_ABI;
      const contract = web3Utils.getContract(contractAddress, abi);

      if (collectionType === "ERC721") {
        if (tokenId) {
          // Approve specific token
          const tx = await contract.approve(
            CONTRACT_ADDRESSES.EXCHANGE,
            tokenId
          );
          await tx.wait();
        } else {
          // Approve all tokens
          const tx = await contract.setApprovalForAll(
            CONTRACT_ADDRESSES.EXCHANGE,
            true
          );
          await tx.wait();
        }
      } else {
        // ERC1155 - always approve all
        const tx = await contract.setApprovalForAll(
          CONTRACT_ADDRESSES.EXCHANGE,
          true
        );
        await tx.wait();
      }
    } catch (error) {
      console.error("Error approving NFT:", error);
      throw error;
    }
  };

  const handleList = async (isBatch = false) => {
    if (!account) {
      setError("Please connect your wallet first");
      return;
    }

    const validationErrors = validateInputs(isBatch);
    if (validationErrors.length > 0) {
      setError(validationErrors.join(", "));
      return;
    }

    setIsProcessing(true);
    setError(null);
    setSuccess(null);

    try {
      const exchangeContract = web3Utils.getContract(
        CONTRACT_ADDRESSES.EXCHANGE,
        NFT_EXCHANGE_ABI
      );

      const durationInSeconds = parseInt(duration) * 24 * 60 * 60; // Convert days to seconds

      if (isBatch) {
        const tokenIdArray = parseArrayInput(tokenIds).map((id) => BigInt(id));
        const priceArray = parseArrayInput(prices).map((price) =>
          web3Utils.parseEther(price)
        );

        let amountArray;
        if (collectionType === "ERC1155") {
          amountArray = amounts.trim()
            ? parseArrayInput(amounts).map((amount) => BigInt(amount))
            : tokenIdArray.map(() => BigInt(1)); // Default to 1 if not specified
        } else {
          amountArray = tokenIdArray.map(() => BigInt(1)); // ERC721 always 1
        }

        // Approve all tokens
        await approveNFT(selectedCollection);
        setSuccess("Approval successful! Listing NFTs...");

        const tx = await exchangeContract.batchListNFT(
          selectedCollection,
          tokenIdArray,
          amountArray,
          priceArray,
          durationInSeconds
        );

        await tx.wait();
        setSuccess(`Successfully listed ${tokenIdArray.length} NFTs!`);
      } else {
        const tokenId = BigInt(tokenIds.trim());
        const price = web3Utils.parseEther(prices.trim());
        const amount =
          collectionType === "ERC1155"
            ? BigInt(amounts.trim() || "1")
            : BigInt(1);

        // Approve token
        await approveNFT(selectedCollection, tokenId);
        setSuccess("Approval successful! Listing NFT...");

        const tx = await exchangeContract.listNFT(
          selectedCollection,
          tokenId,
          amount,
          price,
          durationInSeconds
        );

        await tx.wait();
        setSuccess("Successfully listed NFT!");
      }

      // Reset form
      setTokenIds("");
      setPrices("");
      setAmounts("");
    } catch (error) {
      console.error("Error listing NFT:", error);
      setError(error.message || "Failed to list NFT");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBuy = async (listingId) => {
    // This would be implemented to buy NFTs
    // For now, just a placeholder
    setError("Buy functionality will be implemented in the next component");
  };

  return (
    <div className="marketplace">
      <div className="marketplace-tabs">
        <button
          className={activeTab === "list" ? "active" : ""}
          onClick={() => setActiveTab("list")}
        >
          List NFTs
        </button>
        <button
          className={activeTab === "browse" ? "active" : ""}
          onClick={() => setActiveTab("browse")}
        >
          Browse Marketplace
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}
      {success && <div className="success-message">{success}</div>}

      {activeTab === "list" && (
        <div className="list-section">
          <h2>List Your NFTs</h2>

          <div className="form-group">
            <label>Select Collection:</label>
            <select
              value={selectedCollection}
              onChange={(e) => setSelectedCollection(e.target.value)}
              disabled={isProcessing}
            >
              <option value="">Choose a collection...</option>
              {collections.map((collection) => (
                <option key={collection.address} value={collection.address}>
                  {collection.name} ({collection.symbol}) - {collection.type}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label>Token ID(s):</label>
            <input
              type="text"
              value={tokenIds}
              onChange={(e) => setTokenIds(e.target.value)}
              placeholder="Single: 1 | Batch: 1,2,3,4"
              disabled={isProcessing}
            />
            <small>For batch listing, separate token IDs with commas</small>
          </div>

          {collectionType === "ERC1155" && (
            <div className="form-group">
              <label>Amount(s) (optional, defaults to 1):</label>
              <input
                type="text"
                value={amounts}
                onChange={(e) => setAmounts(e.target.value)}
                placeholder="Single: 5 | Batch: 1,2,3,4"
                disabled={isProcessing}
              />
              <small>For ERC1155 tokens, specify amounts to list</small>
            </div>
          )}

          <div className="form-group">
            <label>Price(s) (ETH):</label>
            <input
              type="text"
              value={prices}
              onChange={(e) => setPrices(e.target.value)}
              placeholder="Single: 0.1 | Batch: 0.1,0.2,0.3,0.4"
              disabled={isProcessing}
            />
            <small>For batch listing, separate prices with commas</small>
          </div>

          <div className="form-group">
            <label>Duration (days):</label>
            <input
              type="number"
              value={duration}
              onChange={(e) => setDuration(e.target.value)}
              min="1"
              max="365"
              disabled={isProcessing}
            />
          </div>

          <div className="list-buttons">
            <button
              onClick={() => handleList(false)}
              disabled={isProcessing || !account || !selectedCollection}
              className="list-button single"
            >
              {isProcessing ? "Processing..." : "List Single NFT"}
            </button>

            <button
              onClick={() => handleList(true)}
              disabled={isProcessing || !account || !selectedCollection}
              className="list-button batch"
            >
              {isProcessing ? "Processing..." : "Batch List NFTs"}
            </button>
          </div>
        </div>
      )}

      {activeTab === "browse" && (
        <div className="browse-section">
          <MarketplaceBrowser account={account} />
        </div>
      )}
    </div>
  );
};

export default Marketplace;
