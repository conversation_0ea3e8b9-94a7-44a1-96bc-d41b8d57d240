import { useState } from "react";
import "./App.css";
import WalletConnect from "./components/WalletConnect";
import CreateCollection from "./components/CreateCollection";
import MintNFT from "./components/MintNFT";
import Marketplace from "./components/Marketplace";

function App() {
  const [account, setAccount] = useState(null);
  const [activeTab, setActiveTab] = useState("wallet");
  const [collections, setCollections] = useState([]);

  const handleWalletConnected = (connectedAccount) => {
    setAccount(connectedAccount);
    if (activeTab === "wallet") {
      setActiveTab("create");
    }
  };

  const handleWalletDisconnected = () => {
    setAccount(null);
    setActiveTab("wallet");
  };

  const handleCollectionCreated = (collection) => {
    setCollections((prev) => [...prev, collection]);
  };

  const tabs = [
    { id: "wallet", label: "Wallet", component: null },
    { id: "create", label: "Create Collection", disabled: !account },
    {
      id: "mint",
      label: "Mint NFT",
      disabled: !account || collections.length === 0,
    },
    {
      id: "marketplace",
      label: "Marketplace",
      disabled: !account || collections.length === 0,
    },
  ];

  return (
    <div className="app">
      <header className="app-header">
        <h1>🎨 NFT Marketplace</h1>
        <p>Create collections, mint NFTs, and trade on the marketplace</p>
      </header>

      <nav className="app-nav">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`nav-tab ${activeTab === tab.id ? "active" : ""} ${
              tab.disabled ? "disabled" : ""
            }`}
            onClick={() => !tab.disabled && setActiveTab(tab.id)}
            disabled={tab.disabled}
          >
            {tab.label}
          </button>
        ))}
      </nav>

      <main className="app-main">
        {activeTab === "wallet" && (
          <div className="tab-content">
            <WalletConnect
              onWalletConnected={handleWalletConnected}
              onWalletDisconnected={handleWalletDisconnected}
            />
            {account && (
              <div className="wallet-status">
                <h3>✅ Wallet Connected!</h3>
                <p>You can now create collections and mint NFTs.</p>
              </div>
            )}
          </div>
        )}

        {activeTab === "create" && account && (
          <div className="tab-content">
            <CreateCollection
              account={account}
              onCollectionCreated={handleCollectionCreated}
            />
            {collections.length > 0 && (
              <div className="collections-list">
                <h3>Your Collections:</h3>
                {collections.map((collection, index) => (
                  <div key={index} className="collection-item">
                    <strong>{collection.name}</strong> ({collection.symbol}) -{" "}
                    {collection.type}
                    <br />
                    <small>{collection.address}</small>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === "mint" && account && collections.length > 0 && (
          <div className="tab-content">
            <MintNFT account={account} collections={collections} />
          </div>
        )}

        {activeTab === "marketplace" && account && collections.length > 0 && (
          <div className="tab-content">
            <Marketplace account={account} collections={collections} />
          </div>
        )}
      </main>

      <footer className="app-footer">
        <p>Built with React + Ethers.js + MetaMask</p>
      </footer>
    </div>
  );
}

export default App;
