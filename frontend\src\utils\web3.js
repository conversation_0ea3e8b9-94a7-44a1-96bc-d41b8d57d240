import { ethers } from 'ethers';
import { NETWORK_CONFIG } from '../contracts/config.js';

// MetaMask connection utilities
export class Web3Utils {
  constructor() {
    this.provider = null;
    this.signer = null;
    this.account = null;
  }

  // Check if MetaMask is installed
  isMetaMaskInstalled() {
    return typeof window !== 'undefined' && typeof window.ethereum !== 'undefined';
  }

  // Connect to MetaMask
  async connectWallet() {
    if (!this.isMetaMaskInstalled()) {
      throw new Error('MetaMask is not installed');
    }

    try {
      // Request account access
      const accounts = await window.ethereum.request({
        method: 'eth_requestAccounts'
      });

      if (accounts.length === 0) {
        throw new Error('No accounts found');
      }

      // Set up provider and signer
      this.provider = new ethers.BrowserProvider(window.ethereum);
      this.signer = await this.provider.getSigner();
      this.account = accounts[0];

      // Check if we're on the correct network
      await this.checkNetwork();

      return {
        account: this.account,
        provider: this.provider,
        signer: this.signer
      };
    } catch (error) {
      console.error('Error connecting to MetaMask:', error);
      throw error;
    }
  }

  // Check and switch to correct network
  async checkNetwork() {
    try {
      const network = await this.provider.getNetwork();
      const currentChainId = `0x${network.chainId.toString(16)}`;

      if (currentChainId !== NETWORK_CONFIG.chainId) {
        await this.switchNetwork();
      }
    } catch (error) {
      console.error('Error checking network:', error);
      throw error;
    }
  }

  // Switch to the correct network
  async switchNetwork() {
    try {
      await window.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: NETWORK_CONFIG.chainId }]
      });
    } catch (switchError) {
      // If the network doesn't exist, add it
      if (switchError.code === 4902) {
        try {
          await window.ethereum.request({
            method: 'wallet_addEthereumChain',
            params: [NETWORK_CONFIG]
          });
        } catch (addError) {
          console.error('Error adding network:', addError);
          throw addError;
        }
      } else {
        console.error('Error switching network:', switchError);
        throw switchError;
      }
    }
  }

  // Get current account
  async getCurrentAccount() {
    if (!this.provider) {
      return null;
    }

    try {
      const accounts = await window.ethereum.request({
        method: 'eth_accounts'
      });
      return accounts.length > 0 ? accounts[0] : null;
    } catch (error) {
      console.error('Error getting current account:', error);
      return null;
    }
  }

  // Get balance
  async getBalance(address) {
    if (!this.provider) {
      throw new Error('Provider not initialized');
    }

    try {
      const balance = await this.provider.getBalance(address);
      return ethers.formatEther(balance);
    } catch (error) {
      console.error('Error getting balance:', error);
      throw error;
    }
  }

  // Format ETH amount
  formatEther(amount) {
    return ethers.formatEther(amount);
  }

  // Parse ETH amount
  parseEther(amount) {
    return ethers.parseEther(amount.toString());
  }

  // Get contract instance
  getContract(address, abi) {
    if (!this.signer) {
      throw new Error('Signer not initialized');
    }
    return new ethers.Contract(address, abi, this.signer);
  }

  // Listen for account changes
  onAccountsChanged(callback) {
    if (window.ethereum) {
      window.ethereum.on('accountsChanged', callback);
    }
  }

  // Listen for network changes
  onChainChanged(callback) {
    if (window.ethereum) {
      window.ethereum.on('chainChanged', callback);
    }
  }

  // Remove event listeners
  removeAllListeners() {
    if (window.ethereum) {
      window.ethereum.removeAllListeners('accountsChanged');
      window.ethereum.removeAllListeners('chainChanged');
    }
  }

  // Disconnect wallet
  disconnect() {
    this.provider = null;
    this.signer = null;
    this.account = null;
    this.removeAllListeners();
  }
}

// Create singleton instance
export const web3Utils = new Web3Utils();

// Helper functions
export const formatAddress = (address) => {
  if (!address) return '';
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
};

export const formatPrice = (price) => {
  return parseFloat(price).toFixed(4);
};

export const isValidAddress = (address) => {
  try {
    return ethers.isAddress(address);
  } catch {
    return false;
  }
};

// Transaction helpers
export const waitForTransaction = async (txHash, provider) => {
  try {
    const receipt = await provider.waitForTransaction(txHash);
    return receipt;
  } catch (error) {
    console.error('Transaction failed:', error);
    throw error;
  }
};

export const estimateGas = async (contract, method, params, value = 0) => {
  try {
    const gasEstimate = await contract[method].estimateGas(...params, {
      value: value
    });
    return gasEstimate;
  } catch (error) {
    console.error('Gas estimation failed:', error);
    throw error;
  }
};
