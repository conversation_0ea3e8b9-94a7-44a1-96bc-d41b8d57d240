# 🎨 NFT Marketplace Frontend

Frontend React application cho NFT Marketplace với tích hợp MetaMask đầy đủ.

## ✨ Tính năng

### 🔗 Kết nối Ví
- Kết nối/ngắt kết nối MetaMask
- Hiển thị thông tin tài khoản và số dư
- Tự động phát hiện thay đổi tài khoản/mạng
- Hỗ trợ local network (Anvil/Hardhat)

### 🏭 Tạo Collection
- Tạo ERC721 Collections (NFT độc nhất)
- Tạo ERC1155 Collections (NFT đa phiên bản)
- Cấu hình đầy đủ: tên, symbol, giá mint, royalty, v.v.
- Validation form và xử lý lỗi

### 🎯 Mint NFT
- Mint đơn lẻ và batch mint
- Chọn collection từ danh sách đã tạo
- Hiển thị thông tin collection và tính toán chi phí
- Hỗ trợ cả ERC721 và ERC1155

### 🏪 Marketplace
- **List NFT**: <PERSON><PERSON>ng bán NFT lên marketplace
- **Batch List**: <PERSON><PERSON><PERSON> bán nhiều NFT cùng lúc
- **Browse**: <PERSON><PERSON>ệ<PERSON> tất cả NFT đang bán
- **Buy**: Mua NFT đơn lẻ hoặc batch buy
- **Auto Approval**: Tự động approve NFT trước khi list

## 🚀 Cách sử dụng

### 1. Khởi động ứng dụng
```bash
cd frontend
npm run dev
```

### 2. Cấu hình MetaMask
- Đảm bảo MetaMask đã được cài đặt
- Thêm local network:
  - Network Name: `Localhost`
  - RPC URL: `http://127.0.0.1:8545`
  - Chain ID: `31337`
  - Currency Symbol: `ETH`

### 3. Đảm bảo contracts đã deploy
- Anvil/Hardhat đang chạy trên port 8545
- Collection Factory: `******************************************`
- NFT Exchange: `******************************************`

## 📱 Hướng dẫn sử dụng từng bước

### Bước 1: Kết nối ví
1. Mở ứng dụng
2. Click "Connect MetaMask"
3. Chấp nhận kết nối trong MetaMask
4. Ứng dụng sẽ tự động chuyển sang tab "Create Collection"

### Bước 2: Tạo Collection
1. Chọn loại collection (ERC721 hoặc ERC1155)
2. Điền thông tin:
   - Collection Name: Tên collection
   - Symbol: Ký hiệu (VD: MAC)
   - Description: Mô tả collection
   - Base Token URI: URL metadata (VD: https://api.example.com/metadata/)
   - Mint Price: Giá mint (ETH)
   - Max Supply: Số lượng tối đa
   - Mint Limit Per Wallet: Giới hạn mint mỗi ví
   - Royalty Fee: Phí royalty (%)
3. Click "Create Collection"
4. Xác nhận transaction trong MetaMask

### Bước 3: Mint NFT
1. Chuyển sang tab "Mint NFT"
2. Chọn collection từ dropdown
3. Nhập địa chỉ người nhận (mặc định là ví của bạn)
4. Chọn số lượng (cho batch mint)
5. Click "Mint Single NFT" hoặc "Batch Mint X NFTs"
6. Xác nhận transaction trong MetaMask

### Bước 4: List NFT lên Marketplace
1. Chuyển sang tab "Marketplace"
2. Ở tab "List NFTs":
   - Chọn collection
   - Nhập Token ID(s) (VD: "1" hoặc "1,2,3,4" cho batch)
   - Nhập Amount(s) (chỉ cho ERC1155)
   - Nhập Price(s) (VD: "0.1" hoặc "0.1,0.2,0.3,0.4")
   - Chọn thời gian (ngày)
3. Click "List Single NFT" hoặc "Batch List NFTs"
4. Xác nhận approve và list transactions

### Bước 5: Mua NFT từ Marketplace
1. Ở tab "Browse Marketplace"
2. Xem danh sách NFT đang bán
3. Chọn NFT muốn mua (checkbox cho batch buy)
4. Click "Buy Now" hoặc "Buy Selected" cho batch
5. Xác nhận transaction trong MetaMask

## 🔧 Cấu trúc Code

```
frontend/src/
├── components/
│   ├── WalletConnect.jsx      # Kết nối MetaMask
│   ├── CreateCollection.jsx   # Tạo collection
│   ├── MintNFT.jsx           # Mint NFT
│   ├── Marketplace.jsx       # List NFT
│   └── MarketplaceBrowser.jsx # Browse & Buy NFT
├── contracts/
│   └── config.js             # Contract ABIs & addresses
├── utils/
│   └── web3.js              # Web3 utilities
├── App.jsx                  # Main app
└── App.css                  # Styling
```

## 🎨 Tính năng UI/UX

- **Responsive Design**: Hoạt động tốt trên mobile và desktop
- **Real-time Updates**: Cập nhật trạng thái real-time
- **Error Handling**: Xử lý lỗi và hiển thị thông báo rõ ràng
- **Loading States**: Hiển thị trạng thái loading khi xử lý
- **Form Validation**: Validate input trước khi submit
- **Batch Operations**: Hỗ trợ thao tác hàng loạt
- **Price Calculation**: Tự động tính toán chi phí bao gồm fees

## 🐛 Troubleshooting

### MetaMask không kết nối được
- Đảm bảo MetaMask đã được cài đặt
- Refresh trang và thử lại
- Kiểm tra network settings

### Transaction thất bại
- Đảm bảo có đủ ETH để trả gas fees
- Kiểm tra contract addresses đã đúng
- Đảm bảo Anvil/Hardhat đang chạy

### NFT không hiển thị trong marketplace
- Đảm bảo đã approve NFT trước khi list
- Kiểm tra listing chưa hết hạn
- Refresh marketplace browser

## 📝 Notes

- Ứng dụng được thiết kế cho local development với Anvil/Hardhat
- Để deploy lên mainnet/testnet, cần cập nhật network config
- Fees được tính khoảng 10% (royalty + marketplace fee)
- Batch operations giúp tiết kiệm gas fees

## 🔮 Tính năng có thể mở rộng

- [ ] Upload metadata lên IPFS
- [ ] Hiển thị hình ảnh NFT
- [ ] Filter và search marketplace
- [ ] Auction functionality
- [ ] Collection analytics
- [ ] User profiles
- [ ] Transaction history
