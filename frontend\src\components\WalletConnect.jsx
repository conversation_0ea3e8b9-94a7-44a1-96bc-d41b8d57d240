import { useState, useEffect } from 'react';
import { web3Utils, formatAddress } from '../utils/web3.js';

const WalletConnect = ({ onWalletConnected, onWalletDisconnected }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [account, setAccount] = useState(null);
  const [balance, setBalance] = useState('0');
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    checkConnection();
    setupEventListeners();

    return () => {
      web3Utils.removeAllListeners();
    };
  }, []);

  const checkConnection = async () => {
    try {
      const currentAccount = await web3Utils.getCurrentAccount();
      if (currentAccount) {
        setAccount(currentAccount);
        setIsConnected(true);
        await updateBalance(currentAccount);
        onWalletConnected?.(currentAccount);
      }
    } catch (error) {
      console.error('Error checking connection:', error);
    }
  };

  const setupEventListeners = () => {
    web3Utils.onAccountsChanged(handleAccountsChanged);
    web3Utils.onChainChanged(handleChainChanged);
  };

  const handleAccountsChanged = (accounts) => {
    if (accounts.length === 0) {
      handleDisconnect();
    } else {
      setAccount(accounts[0]);
      updateBalance(accounts[0]);
      onWalletConnected?.(accounts[0]);
    }
  };

  const handleChainChanged = () => {
    // Reload the page when chain changes
    window.location.reload();
  };

  const updateBalance = async (address) => {
    try {
      const balance = await web3Utils.getBalance(address);
      setBalance(balance);
    } catch (error) {
      console.error('Error updating balance:', error);
    }
  };

  const connectWallet = async () => {
    if (!web3Utils.isMetaMaskInstalled()) {
      setError('MetaMask is not installed. Please install MetaMask to continue.');
      return;
    }

    setIsConnecting(true);
    setError(null);

    try {
      const { account } = await web3Utils.connectWallet();
      setAccount(account);
      setIsConnected(true);
      await updateBalance(account);
      onWalletConnected?.(account);
    } catch (error) {
      console.error('Error connecting wallet:', error);
      setError(error.message || 'Failed to connect wallet');
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = () => {
    web3Utils.disconnect();
    setIsConnected(false);
    setAccount(null);
    setBalance('0');
    setError(null);
    onWalletDisconnected?.();
  };

  if (!web3Utils.isMetaMaskInstalled()) {
    return (
      <div className="wallet-connect">
        <div className="wallet-error">
          <h3>MetaMask Required</h3>
          <p>Please install MetaMask to use this application.</p>
          <a 
            href="https://metamask.io/download/" 
            target="_blank" 
            rel="noopener noreferrer"
            className="install-button"
          >
            Install MetaMask
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="wallet-connect">
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {!isConnected ? (
        <button 
          onClick={connectWallet} 
          disabled={isConnecting}
          className="connect-button"
        >
          {isConnecting ? 'Connecting...' : 'Connect MetaMask'}
        </button>
      ) : (
        <div className="wallet-info">
          <div className="account-info">
            <div className="account-address">
              <strong>Account:</strong> {formatAddress(account)}
            </div>
            <div className="account-balance">
              <strong>Balance:</strong> {parseFloat(balance).toFixed(4)} ETH
            </div>
          </div>
          <button 
            onClick={handleDisconnect}
            className="disconnect-button"
          >
            Disconnect
          </button>
        </div>
      )}
    </div>
  );
};

export default WalletConnect;
