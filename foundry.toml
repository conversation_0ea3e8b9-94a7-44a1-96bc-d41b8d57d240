[profile.default]
src = "src"
test = "test"
out = "out"
libs = ["lib"]
optimizer = true
optimizer_runs = 200
fs_permissions = [{ access = "read-write", path = "./" }]

remappings = ['@openzeppelin/contracts=lib/openzeppelin-contracts/contracts']

# Network configurations
[rpc_endpoints]
localhost = "http://localhost:8545"
anvil = "http://127.0.0.1:8545"

# See more config options https://github.com/foundry-rs/foundry/blob/master/crates/config/README.md#all-options
