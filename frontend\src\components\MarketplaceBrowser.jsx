import { useState, useEffect } from 'react';
import { web3Utils, formatAddress, formatPrice } from '../utils/web3.js';
import { CONTRACT_ADDRESSES, NFT_EXCHANGE_ABI } from '../contracts/config.js';

const MarketplaceBrowser = ({ account }) => {
  const [listings, setListings] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [selectedListings, setSelectedListings] = useState([]);
  const [isBuying, setIsBuying] = useState(false);

  useEffect(() => {
    if (account) {
      loadListings();
    }
  }, [account]);

  const loadListings = async () => {
    setLoading(true);
    setError(null);

    try {
      const exchangeContract = web3Utils.getContract(
        CONTRACT_ADDRESSES.EXCHANGE,
        NFT_EXCHANGE_ABI
      );

      // Get NFTListed events
      const filter = exchangeContract.filters.NFTListed();
      const events = await exchangeContract.queryFilter(filter, -10000); // Last 10000 blocks

      const listingsData = [];

      for (const event of events) {
        try {
          const listingId = event.args.listingId;
          
          // Get current listing status
          const listing = await exchangeContract.getListing(listingId);
          
          // Only show active listings (status = 0)
          if (listing.status === 0n) {
            listingsData.push({
              listingId,
              contractAddress: listing.contractAddress,
              tokenId: listing.tokenId.toString(),
              price: web3Utils.formatEther(listing.price),
              seller: listing.seller,
              duration: listing.duration.toString(),
              start: listing.start.toString(),
              amount: listing.amount.toString(),
              // Additional data from event
              blockNumber: event.blockNumber,
              transactionHash: event.transactionHash
            });
          }
        } catch (err) {
          console.error('Error processing listing:', err);
        }
      }

      // Sort by newest first
      listingsData.sort((a, b) => b.blockNumber - a.blockNumber);
      setListings(listingsData);

    } catch (error) {
      console.error('Error loading listings:', error);
      setError('Failed to load marketplace listings');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectListing = (listingId) => {
    setSelectedListings(prev => {
      if (prev.includes(listingId)) {
        return prev.filter(id => id !== listingId);
      } else {
        return [...prev, listingId];
      }
    });
  };

  const calculateTotalPrice = () => {
    return selectedListings.reduce((total, listingId) => {
      const listing = listings.find(l => l.listingId === listingId);
      return total + parseFloat(listing?.price || 0);
    }, 0);
  };

  const buyNFT = async (listingId, isBatch = false) => {
    if (!account) {
      setError('Please connect your wallet first');
      return;
    }

    setIsBuying(true);
    setError(null);
    setSuccess(null);

    try {
      const exchangeContract = web3Utils.getContract(
        CONTRACT_ADDRESSES.EXCHANGE,
        NFT_EXCHANGE_ABI
      );

      let tx;
      let totalPrice = 0;

      if (isBatch) {
        if (selectedListings.length === 0) {
          setError('Please select at least one NFT to buy');
          return;
        }

        // Calculate total price including fees
        for (const id of selectedListings) {
          const listing = listings.find(l => l.listingId === id);
          if (listing) {
            // Add ~10% for fees (royalty + marketplace fee)
            totalPrice += parseFloat(listing.price) * 1.1;
          }
        }

        tx = await exchangeContract.batchBuyNFT(selectedListings, {
          value: web3Utils.parseEther(totalPrice.toString())
        });

        setSuccess('Batch purchase transaction submitted! Waiting for confirmation...');
      } else {
        const listing = listings.find(l => l.listingId === listingId);
        if (!listing) {
          setError('Listing not found');
          return;
        }

        // Add ~10% for fees
        totalPrice = parseFloat(listing.price) * 1.1;

        tx = await exchangeContract.buyNFT(listingId, {
          value: web3Utils.parseEther(totalPrice.toString())
        });

        setSuccess('Purchase transaction submitted! Waiting for confirmation...');
      }

      const receipt = await tx.wait();
      
      if (isBatch) {
        setSuccess(`Successfully purchased ${selectedListings.length} NFTs! Transaction: ${receipt.hash}`);
        setSelectedListings([]);
      } else {
        setSuccess(`Successfully purchased NFT! Transaction: ${receipt.hash}`);
      }

      // Reload listings to update status
      await loadListings();

    } catch (error) {
      console.error('Error buying NFT:', error);
      setError(error.message || 'Failed to purchase NFT');
    } finally {
      setIsBuying(false);
    }
  };

  const isExpired = (start, duration) => {
    const startTime = parseInt(start);
    const durationSeconds = parseInt(duration);
    const expiryTime = startTime + durationSeconds;
    const currentTime = Math.floor(Date.now() / 1000);
    return currentTime > expiryTime;
  };

  const formatTimeRemaining = (start, duration) => {
    const startTime = parseInt(start);
    const durationSeconds = parseInt(duration);
    const expiryTime = startTime + durationSeconds;
    const currentTime = Math.floor(Date.now() / 1000);
    const remaining = expiryTime - currentTime;

    if (remaining <= 0) return 'Expired';

    const days = Math.floor(remaining / (24 * 60 * 60));
    const hours = Math.floor((remaining % (24 * 60 * 60)) / (60 * 60));
    const minutes = Math.floor((remaining % (60 * 60)) / 60);

    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  return (
    <div className="marketplace-browser">
      <div className="browser-header">
        <h2>Browse Marketplace</h2>
        <button 
          onClick={loadListings} 
          disabled={loading}
          className="refresh-button"
        >
          {loading ? 'Loading...' : 'Refresh'}
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}
      {success && <div className="success-message">{success}</div>}

      {selectedListings.length > 0 && (
        <div className="batch-buy-section">
          <div className="batch-info">
            <span>Selected: {selectedListings.length} NFTs</span>
            <span>Total: ~{formatPrice(calculateTotalPrice() * 1.1)} ETH (including fees)</span>
          </div>
          <button
            onClick={() => buyNFT(null, true)}
            disabled={isBuying}
            className="batch-buy-button"
          >
            {isBuying ? 'Purchasing...' : `Buy Selected (${selectedListings.length})`}
          </button>
        </div>
      )}

      <div className="listings-grid">
        {loading ? (
          <div className="loading">Loading marketplace...</div>
        ) : listings.length === 0 ? (
          <div className="no-listings">
            <h3>No NFTs listed for sale</h3>
            <p>Be the first to list an NFT on the marketplace!</p>
          </div>
        ) : (
          listings.map((listing) => (
            <div 
              key={listing.listingId} 
              className={`listing-card ${isExpired(listing.start, listing.duration) ? 'expired' : ''}`}
            >
              <div className="listing-header">
                <input
                  type="checkbox"
                  checked={selectedListings.includes(listing.listingId)}
                  onChange={() => handleSelectListing(listing.listingId)}
                  disabled={isExpired(listing.start, listing.duration) || listing.seller === account}
                />
                <span className="token-id">#{listing.tokenId}</span>
              </div>

              <div className="listing-info">
                <div className="contract-address">
                  <strong>Collection:</strong> {formatAddress(listing.contractAddress)}
                </div>
                <div className="price">
                  <strong>Price:</strong> {formatPrice(listing.price)} ETH
                </div>
                <div className="seller">
                  <strong>Seller:</strong> {formatAddress(listing.seller)}
                  {listing.seller === account && <span className="own-listing"> (Your listing)</span>}
                </div>
                <div className="amount">
                  <strong>Amount:</strong> {listing.amount}
                </div>
                <div className="time-remaining">
                  <strong>Time left:</strong> {formatTimeRemaining(listing.start, listing.duration)}
                </div>
              </div>

              <div className="listing-actions">
                {listing.seller === account ? (
                  <button className="own-listing-button" disabled>
                    Your Listing
                  </button>
                ) : isExpired(listing.start, listing.duration) ? (
                  <button className="expired-button" disabled>
                    Expired
                  </button>
                ) : (
                  <button
                    onClick={() => buyNFT(listing.listingId, false)}
                    disabled={isBuying}
                    className="buy-button"
                  >
                    {isBuying ? 'Buying...' : 'Buy Now'}
                  </button>
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default MarketplaceBrowser;
