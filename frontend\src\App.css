/* App Layout */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
}

.app-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1rem;
}

/* Navigation */
.app-nav {
  display: flex;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  padding: 0 2rem;
  gap: 0;
}

.nav-tab {
  background: none;
  border: none;
  padding: 1rem 2rem;
  cursor: pointer;
  font-size: 1rem;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.nav-tab:hover:not(.disabled) {
  background: #f5f5f5;
  color: #333;
}

.nav-tab.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: #f8f9ff;
}

.nav-tab.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Main Content */
.app-main {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.tab-content {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Footer */
.app-footer {
  background: #f8f9fa;
  padding: 1rem;
  text-align: center;
  color: #666;
  border-top: 1px solid #e0e0e0;
}

/* Wallet Connect */
.wallet-connect {
  text-align: center;
  padding: 2rem;
}

.connect-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.connect-button:hover {
  transform: translateY(-2px);
}

.connect-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.wallet-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9ff;
  padding: 1rem;
  border-radius: 8px;
  border: 2px solid #667eea;
}

.account-info {
  text-align: left;
}

.account-address,
.account-balance {
  margin: 0.25rem 0;
}

.disconnect-button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
}

.wallet-status {
  margin-top: 2rem;
  padding: 1rem;
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  color: #155724;
}

.wallet-error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  padding: 2rem;
  color: #721c24;
}

.install-button {
  display: inline-block;
  background: #ff6b35;
  color: white;
  padding: 1rem 2rem;
  text-decoration: none;
  border-radius: 8px;
  margin-top: 1rem;
  transition: background 0.3s ease;
}

.install-button:hover {
  background: #e55a2b;
}

/* Messages */
.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid #f5c6cb;
}

.success-message {
  background: #d4edda;
  color: #155724;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid #c3e6cb;
}

/* Forms */
.collection-form,
.mint-form {
  max-width: 600px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.form-group small {
  display: block;
  margin-top: 0.25rem;
  color: #666;
  font-size: 0.875rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* Buttons */
.create-button,
.mint-button,
.list-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: 1rem;
}

.create-button:hover,
.mint-button:hover,
.list-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.create-button:disabled,
.mint-button:disabled,
.list-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.mint-buttons,
.list-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-top: 1rem;
}

.mint-button.batch,
.list-button.batch {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

/* Collection Info */
.collection-info {
  background: #f8f9ff;
  border: 2px solid #667eea;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

.collection-info h3 {
  margin: 0 0 0.5rem 0;
  color: #667eea;
}

.collection-info p {
  margin: 0.25rem 0;
}

.price-info {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

.price-info p {
  margin: 0.25rem 0;
}

/* Collections List */
.collections-list {
  margin-top: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.collections-list h3 {
  margin: 0 0 1rem 0;
  color: #333;
}

.collection-item {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  border-left: 4px solid #667eea;
}

.collection-item:last-child {
  margin-bottom: 0;
}

.collection-item strong {
  color: #333;
}

.collection-item small {
  color: #666;
  font-family: monospace;
}

/* Marketplace */
.marketplace-tabs {
  display: flex;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e0e0e0;
}

.marketplace-tabs button {
  background: none;
  border: none;
  padding: 1rem 2rem;
  cursor: pointer;
  font-size: 1rem;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.marketplace-tabs button:hover {
  background: #f5f5f5;
  color: #333;
}

.marketplace-tabs button.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: #f8f9ff;
}

.list-section,
.browse-section {
  max-width: 600px;
  margin: 0 auto;
}

/* Marketplace Browser */
.marketplace-browser {
  max-width: 1000px;
  margin: 0 auto;
}

.browser-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.refresh-button {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.refresh-button:hover {
  background: #5a6268;
}

.refresh-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.batch-buy-section {
  background: #e7f3ff;
  border: 2px solid #007bff;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-info {
  display: flex;
  gap: 2rem;
  font-weight: 600;
}

.batch-buy-button {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: transform 0.2s ease;
}

.batch-buy-button:hover {
  transform: translateY(-2px);
}

.batch-buy-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.listings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.listing-card {
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
}

.listing-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.listing-card.expired {
  opacity: 0.6;
  border-color: #dc3545;
}

.listing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.listing-header input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.token-id {
  font-weight: 600;
  color: #667eea;
  font-size: 1.1rem;
}

.listing-info {
  margin-bottom: 1.5rem;
}

.listing-info > div {
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.listing-info strong {
  color: #333;
}

.contract-address,
.seller {
  font-family: monospace;
  font-size: 0.85rem;
}

.price {
  font-size: 1.1rem;
  color: #28a745;
  font-weight: 600;
}

.own-listing {
  color: #007bff;
  font-weight: 600;
  font-size: 0.8rem;
}

.time-remaining {
  color: #fd7e14;
  font-weight: 600;
}

.listing-actions {
  display: flex;
  gap: 0.5rem;
}

.buy-button {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  width: 100%;
  transition: transform 0.2s ease;
}

.buy-button:hover {
  transform: translateY(-2px);
}

.buy-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.own-listing-button,
.expired-button {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  width: 100%;
  cursor: not-allowed;
  opacity: 0.7;
}

.expired-button {
  background: #dc3545;
}

.loading,
.no-listings {
  text-align: center;
  padding: 3rem;
  color: #666;
  grid-column: 1 / -1;
}

.no-listings h3 {
  margin-bottom: 1rem;
  color: #333;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-main {
    padding: 1rem;
  }

  .tab-content {
    padding: 1rem;
  }

  .app-nav {
    padding: 0 1rem;
    overflow-x: auto;
  }

  .nav-tab {
    padding: 1rem;
    white-space: nowrap;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .mint-buttons,
  .list-buttons {
    grid-template-columns: 1fr;
  }

  .wallet-info {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .listings-grid {
    grid-template-columns: 1fr;
  }

  .batch-buy-section {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .batch-info {
    flex-direction: column;
    gap: 0.5rem;
  }
}
